<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browser Use - Sandbox on AWS Demo UI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/style.css">
    <style>
        .nav-tabs .nav-link {
            color: #495057;
            font-weight: 500;
        }
        .nav-tabs .nav-link.active {
            color: #0d6efd;
            font-weight: 600;
        }
        .coming-soon-badge {
            font-size: 0.65rem;
            margin-left: 5px;
            padding: 0.25em 0.5em;
            vertical-align: middle;
            font-weight: normal;
        }

        /* GitHub link styles */
        .github-link {
            color: #333;
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 6px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            transition: all 0.2s ease-in-out;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .github-link:hover {
            color: #0d6efd;
            background-color: #e9ecef;
            border-color: #0d6efd;
            text-decoration: none;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .github-icon {
            width: 20px;
            height: 20px;
            fill: currentColor;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row header">
            <div class="col-12 d-flex justify-content-between align-items-center">
                <h1>Sandbox on AWS Demo UI</h1>
                <a href="https://github.com/teaguexiao/sandbox-on-aws-demo" target="_blank" rel="noopener noreferrer" class="github-link">
                    <svg class="github-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 0C5.374 0 0 5.373 0 12 0 17.302 3.438 21.8 8.207 23.387c.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23A11.509 11.509 0 0112 5.803c1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576C20.566 21.797 24 17.3 24 12c0-6.627-5.373-12-12-12z"/>
                    </svg>
                    View Source
                </a>
            </div>
        </div>
        
        <!-- Navigation Tabs -->
        <div class="row mb-3">
            <div class="col-12">
                <ul class="nav nav-tabs" id="sandboxTabs">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/sandbox-lifecycle">Sandbox Lifecycle</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/browser-use">Browser Use</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/code-interpreter">Code Interpreter</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/computer-use">Computer Use</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/ai-search">AI Search <span class="badge bg-warning text-dark coming-soon-badge">Coming Soon</span></a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/ai-ppt">AI PPT <span class="badge bg-warning text-dark coming-soon-badge">Coming Soon</span></a>
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="row main-content">
            <div class="col-12 mt-4">
                <!-- Modern, smaller sub-tabs for Browser Use -->
                <div class="d-flex justify-content-center mb-4">
                    <div class="btn-group" role="group" aria-label="Browser Use Options">
                        <a href="/browser-use" class="btn btn-sm btn-primary active" id="e2b-tab" aria-current="page">E2B Desktop</a>
                        <a href="/browser-use-agentcore" class="btn btn-sm btn-outline-primary" id="agentcore-tab">Agentcore BrowserTool</a>
                    </div>
                </div>

                <!-- Content for E2B Desktop tab (default) -->
                <div class="tab-content">
                    <div class="tab-pane fade show active" id="e2b-content">
                        <div class="row main-content">
                            <!-- Left panel: Desktop stream -->
                            <div class="col-md-7 stream-panel">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h3>Desktop Stream - Power by E2B on AWS</h3>
                                        <div id="sandbox-controls" class="d-flex align-items-center" style="display: none;">
                                            <span id="sandbox-timer" class="badge bg-secondary me-2 py-2 px-3 fs-6">00:00</span>
                                            <span id="sandbox-id" class="badge bg-info me-2"></span>
                                            <button id="stop-desktop" class="btn btn-danger py-1 px-2 fs-6">Stop</button>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div id="stream-container">
                                            <div id="stream-placeholder">
                                                <p>No active stream. Start a new desktop to begin.</p>
                                            </div>
                                            <iframe id="stream-frame" style="display: none;"></iframe>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Right panel: Controls and logs -->
                            <div class="col-md-5 control-panel">
                                <div class="card">
                                    <div class="card-header">
                                        <h3>Controls</h3>
                                    </div>
                                    <div class="card-body">
                                        <form id="task-form">
                                            <div class="mb-3">
                                                <label for="task-input" class="form-label">Task Prompt:</label>
                                                <textarea id="task-input" class="form-control" rows="4" placeholder="Enter your task here..."></textarea>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">Example Tasks:</label>
                                                <div class="d-flex flex-wrap gap-2 mb-2">
                                                    <button type="button" class="btn btn-sm btn-outline-secondary example-task" data-prompt="Search for recent news about artificial intelligence breakthroughs and summarize the top 3 developments.">AI News</button>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary example-task" data-prompt="Find and compare prices for iPhone 15 Pro Max across major online retailers. Create a comparison table.">Price Compare</button>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary example-task" data-prompt="Research the most popular tourist destinations in Japan and create an itinerary for a 7-day trip.">Travel Plan</button>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary example-task" data-prompt="Find the best-rated restaurants in San Francisco with vegetarian options and outdoor seating.">Restaurant Search</button>
                                                </div>
                                            </div>

                                            <div class="d-flex flex-wrap gap-2 mb-4">
                                                <button type="button" id="run-workflow" class="btn btn-sm btn-info">Run Task</button>
                                            </div>

                                            <div class="logs-section mt-4">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <h4>Logs</h4>
                                                    <button id="clear-logs" class="btn btn-sm btn-outline-secondary">Clear</button>
                                                </div>
                                                <div id="log-container">
                                                    <div id="logs"></div>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/script.js"></script>
    <script src="/static/js/agentcore-browser.js"></script>
    
    <!-- Agentcore Browser Fullscreen Modal -->
    <div class="modal fade" id="agentcoreFullscreenModal" tabindex="-1" aria-labelledby="agentcoreFullscreenModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-fullscreen">
            <div class="modal-content">
                <div class="modal-header bg-dark text-white">
                    <h5 class="modal-title" id="agentcoreFullscreenModalLabel">
                        <i class="fas fa-globe me-2"></i>Agentcore Browser - Fullscreen View
                    </h5>
                    <button type="button" class="btn btn-outline-light btn-sm" data-bs-dismiss="modal" aria-label="Close">
                        <i class="fas fa-times me-1"></i>Exit Fullscreen
                    </button>
                </div>
                <div class="modal-body p-0">
                    <div id="agentcore-fullscreen-container" style="width: 100%; height: calc(100vh - 60px);">
                        <!-- Browser iframe will be moved here when in fullscreen mode -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Modal -->
    <div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="imageModalLabel">Image Preview</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" class="img-fluid" alt="Enlarged Image">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
