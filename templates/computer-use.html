<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Computer Use - Sandbox on AWS Demo UI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/style.css">
    <style>
        .nav-tabs .nav-link {
            color: #495057;
            font-weight: 500;
        }
        .nav-tabs .nav-link.active {
            color: #0d6efd;
            font-weight: 600;
        }
        .coming-soon-badge {
            font-size: 0.65rem;
            margin-left: 5px;
            padding: 0.25em 0.5em;
            vertical-align: middle;
            font-weight: normal;
        }

        /* Force left alignment for all log content */
        #log-container, #logs, .log-entry {
            text-align: left !important;
            justify-content: flex-start !important;
            align-items: flex-start !important;
        }

        /* Ensure badges and timestamps don't get centered */
        #logs .log-entry .badge,
        #logs .log-entry .text-muted {
            margin-left: 0 !important;
            text-align: left !important;
        }

        /* GitHub link styles */
        .github-link {
            color: #333;
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 6px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            transition: all 0.2s ease-in-out;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .github-link:hover {
            color: #0d6efd;
            background-color: #e9ecef;
            border-color: #0d6efd;
            text-decoration: none;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .github-icon {
            width: 20px;
            height: 20px;
            fill: currentColor;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row header">
            <div class="col-12 d-flex justify-content-between align-items-center">
                <h1>Sandbox on AWS Demo UI</h1>
                <a href="https://github.com/teaguexiao/sandbox-on-aws-demo" target="_blank" rel="noopener noreferrer" class="github-link">
                    <svg class="github-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 0C5.374 0 0 5.373 0 12 0 17.302 3.438 21.8 8.207 23.387c.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23A11.509 11.509 0 0112 5.803c1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576C20.566 21.797 24 17.3 24 12c0-6.627-5.373-12-12-12z"/>
                    </svg>
                    View Source
                </a>
            </div>
        </div>
        
        <!-- Navigation Tabs -->
        <div class="row mb-3">
            <div class="col-12">
                <ul class="nav nav-tabs" id="sandboxTabs">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/sandbox-lifecycle">Sandbox Lifecycle</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/browser-use">Browser Use</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/code-interpreter">Code Interpreter</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/computer-use">Computer Use</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/ai-search">AI Search <span class="badge bg-warning text-dark coming-soon-badge">Coming Soon</span></a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/ai-ppt">AI PPT <span class="badge bg-warning text-dark coming-soon-badge">Coming Soon</span></a>
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="row main-content">
            <!-- Left panel: Desktop stream -->
            <div class="col-md-7 stream-panel">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h3>Desktop Stream - Claude Computer Use on E2B</h3>
                        <div id="sandbox-controls" class="d-flex align-items-center" style="display: none;">
                            <span id="sandbox-timer" class="badge bg-secondary me-2 py-2 px-3 fs-6">00:00</span>
                            <span id="sandbox-id" class="badge bg-info me-2"></span>
                            <button id="stop-desktop" class="btn btn-danger py-1 px-2 fs-6">Stop</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="stream-container">
                            <div id="stream-placeholder">
                                <p>No active stream. Start a new desktop to begin computer use tasks.</p>
                            </div>
                            <iframe id="stream-frame" style="display: none; width: 100%; border: none;"></iframe>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Right panel: Controls and logs -->
            <div class="col-md-5 control-panel">
                <div class="card">
                    <div class="card-header">
                        <h3>Computer Use Controls</h3>
                    </div>
                    <div class="card-body">
                        <form id="computer-use-form">
                            <div class="mb-3">
                                <label for="task-input" class="form-label">Task for Claude:</label>
                                <textarea id="task-input" class="form-control" rows="4" placeholder="Describe what you want Claude to do on the computer..."></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Example Computer Use Tasks:</label>
                                <div class="d-flex flex-wrap gap-2 mb-2">
                                    <button type="button" class="btn btn-sm btn-outline-primary example-task" data-prompt="Open Firefox and search for 'latest AI news' on Google">Web Browse</button>
                                    <button type="button" class="btn btn-sm btn-outline-primary example-task" data-prompt="Open VS Code and create a Python script that prints 'Hello World'">Code Editor</button>
                                    <button type="button" class="btn btn-sm btn-outline-primary example-task" data-prompt="Open the file manager and create a new folder called 'test-folder'">File Mgmt</button>
                                    <button type="button" class="btn btn-sm btn-outline-primary example-task" data-prompt="Open LibreOffice Writer and write a short paragraph about AI">Document</button>
                                    <button type="button" class="btn btn-sm btn-outline-primary example-task" data-prompt="Open the terminal and run 'ls -la' to list files">Terminal</button>
                                    <button type="button" class="btn btn-sm btn-outline-primary example-task" data-prompt="Take a screenshot and tell me what applications are visible on the desktop">Screenshot</button>
                                </div>
                            </div>
                            
                            <div class="d-flex flex-wrap gap-2 mb-4">
                                <button type="button" id="start-computer-use" class="btn btn-success">Start Desktop & Task</button>
                                <button type="button" id="run-computer-task" class="btn btn-primary" disabled>Run Task</button>
                                <button type="button" id="take-screenshot" class="btn btn-info" disabled>Take Screenshot</button>
                                <button type="button" id="stop-task" class="btn btn-warning" disabled>Stop Task</button>
                            </div>
                            
                            <!-- Action display -->
                            <div id="current-action" class="alert alert-info" style="display: none;">
                                <strong>Current Action:</strong> <span id="action-description"></span>
                            </div>
                            
                            <!-- Reasoning display -->
                            <div id="reasoning-section" style="display: none;">
                                <h5>Claude's Reasoning:</h5>
                                <div id="reasoning-content" class="border rounded p-3 bg-light mb-3" style="max-height: 200px; overflow-y: auto;"></div>
                            </div>
                            
                            <div class="logs-section mt-4" style="text-align: left !important;">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h4>Computer Use Logs</h4>
                                    <button id="clear-logs" class="btn btn-sm btn-outline-secondary">Clear</button>
                                </div>
                                <div id="log-container" style="text-align: left !important; max-height: 400px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 0.375rem; padding: 0.75rem; background-color: #f8f9fa;">
                                    <div id="logs" style="text-align: left !important;"></div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/computer-use.js"></script>
</body>
</html>
